##少样提示
# 加载环境变量
import os
from openai import OpenAI    #pip install  openai    python_dotenv

#加载环境变量
from dotenv import load_dotenv, find_dotenv
_ = load_dotenv(find_dotenv())  # 读取本地 .env 文件，里面定义了 OPENAI_API_KEY

openai = OpenAI(
    api_key=os.getenv("OPENAI_API_KEY"),
    base_url=os.getenv("OPENAI_API_BASE"),
)
#model = 'gpt-3.5-turbo'
# 基于 prompt 生成文本
def get_completion(prompt, model="gpt-4o"):
    messages = [{"role": "user", "content": prompt}]
    response = openai.chat.completions.create(
        model=model,
        messages=messages,
        temperature=0,  # 模型输出的随机性，0 表示随机性最小
    )
    return response.choices[0].message.content

prompt = """'whatpu'是坦桑尼亚的一种小型毛茸茸的动物。\
             一个使用whatpu这个词的句子的例子是：我们在非洲旅行时看到了这些非常可爱的whatpus。\
            'farduddle'是指快速跳上跳下。一个使用farduddle这个词的句子的例子是："""
response = get_completion(prompt)
print(response)

