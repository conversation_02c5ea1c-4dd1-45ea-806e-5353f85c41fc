
1、 安装Anaconda或者Miniconda
2、 安装PyCharm IDE
3、 申请OPENAI_API_KEY，项目中创建.env文件，配置OPENAI_API_KEY和OPENAI_API_BASE
    格式如下:
            OPENAI_API_KEY=sk-2y7285m3DI6VqFuPPmFqBUJ3VRRyyC5kjhtEWI6rMeHkHXSX
            OPENAI_API_BASE=https://api.openai-proxy.org/v1
4、 创建虚拟环境并制定python 版本
conda create --name myenv python=3.11 -y
5、 ‌激活环境‌：conda activate myenv
6、 跑测试代码





附录：conda常用命令
‌查看conda版本‌：
conda --version 或 conda -V
‌更新conda及其所有包‌：
conda update conda
conda update --all
‌创建新环境‌和创建指定Python版本新环境：
conda create --name myenv 或者  conda create -n myenv
conda create --name myenv python=x.x
‌激活和退出环境‌：
conda activate myenv
conda deactivate
‌列出所有环境‌：
conda env list 或 conda info -e
‌安装包‌：
conda install package_name
‌更新包‌：
conda update package_name
‌删除环境‌：
conda env remove -n env_name
‌导出和导入环境配置‌：
conda list -e > requirements.txt（导出环境配置）
conda install --yes --file requirements.txt（导入环境配置）