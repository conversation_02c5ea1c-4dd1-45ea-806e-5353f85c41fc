import os
from dotenv import load_dotenv, find_dotenv
# 加载环境变量
load_dotenv(find_dotenv())


# 获取 OpenAI API 密钥
OPENAI_API_KEY = os.environ['OPENAI_API_KEY']
OPENAI_API_BASE = os.environ['OPENAI_API_BASE']
##智普ChatGLM SDK
Zhipu_API_KEY=os.environ['Zhipu_API_KEY']
##阿里千问
Qwen_API_KEY = os.environ['Qwen_API_KEY']
##Baichuan SDK KEY---用的阿里千问的KEY
Baichuan_API_KEY = os.environ['Baichuan_API_KEY']
Baichuan_API_KEY_HTTP = os.environ['Baichuan_API_KEY_HTTP']
##LLaMA KEY--用的是百度千帆的HTTP KEY
LLaMA_API_KEY = os.environ['LLaMA_API_KEY']
LLaMA_SECRET_KEY = os.environ['LLaMA_SECRET_KEY']
##Yi-34B SDK
Yi_API_KEY = os.environ['Yi_API_KEY']
Yi_SECRET_KEY = os.environ['Yi_SECRET_KEY']


