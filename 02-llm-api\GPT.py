from config import OPENAI_API_KEY,OPENAI_API_BASE
from openai import OpenAI

openai = OpenAI(
    api_key=OPENAI_API_KEY,
    base_url=OPENAI_API_BASE,
)
# model_list = openai.models.list()
# print(model_list)

model = 'gpt-4'
# 基于 prompt 生成文本   system user assistant function  类open_ai
def get_completion(prompt, model="gpt-4"):
    messages = [{"role": "user", "content": prompt}]
    #Role: system  user  function calling, AI_assitant
    #API:https://api.openai.com/v1/chat/completions
    response = openai.chat.completions.create(
        model=model,
        messages=messages,
        temperature=0,  # 模型输出的随机性，0 表示随机性最小
    )
    return response.choices[0].message.content
prompt = "给我讲一个笑话"
response = get_completion(prompt)
print(response)

# #
# # 基于 prompt 生成文本
# def get_completion(messages, model="gpt-3.5-turbo"):
#     messages = messages
#     response = openai.chat.completions.create(
#         model=model,
#         messages=messages,
#         temperature=0,  # 模型输出的随机性，0 表示随机性最小
#     )
#     return response.choices[0].message.content
#
# messages = [
#                 {"role": "system", "content": "你是AI助手小懿，你是小懿学堂的助教，这门课每周二、四上课。"},
#                 {"role": "user", "content": "你是谁？负责干什么的？我们什么时间上课？"},
#
#             ]
# response = get_completion(messages)
# print(response)



