"""
    演示--单轮对话和多轮对话
"""
# 加载环境变量
from config import OPENAI_API_KEY,OPENAI_API_BASE
import json
from openai import OpenAI

openai = OpenAI(
    api_key=OPENAI_API_KEY,
    base_url=OPENAI_API_BASE
)

session = [
    {
        "role": "system",
        "content": """
                        你是一个手机流量套餐的客服代表，你叫小懿。可以帮助用户选择最合适的流量套餐产品。可以选择的套餐包括：
                        经济套餐，月费50元，10G流量；
                        畅游套餐，月费180元，100G流量；
                        无限套餐，月费300元，1000G流量；
                        校园套餐，月费150元，200G流量，仅限在校生。
                    """
    }
]

def get_completion(prompt, model="gpt-3.5-turbo"):
    session.append({"role": "user", "content": prompt})
    response = openai.chat.completions.create(
        model=model,
        messages=session,
        temperature=0,  # 模型输出的随机性，0 表示随机性最小
    )
    msg = response.choices[0].message.content
    session.append({"role": "assistant", "content": msg})
    return msg


get_completion("有没有无限套餐？")
get_completion("多少钱？")
get_completion("给我办一个")
print(json.dumps(session, indent=4, ensure_ascii=False))  # 用易读格式打印对话历史


