"""
    GPT 初探 -- 演示GPT工作原理
"""
# 加载环境变量
import os
from openai import OpenAI
#pip install openai,python_dotenv

#加载环境变量
from dotenv import load_dotenv, find_dotenv
_ = load_dotenv(find_dotenv())  # 读取本地 .env 文件，里面定义了 OPENAI_API_KEY

openai = OpenAI(
    api_key=os.getenv("OPENAI_API_KEY"),
    base_url=os.getenv("OPENAI_API_BASE"),
)

# model_list = openai.models.list()
# print(model_list)

# 基于 prompt 生成文本
prompt = "徐老师今天很"
responses = openai.completions.create(
    model="gpt-3.5-turbo-instruct",
    prompt=prompt,
    max_tokens=100,
    temperature=0,  # 模型输出的随机性，0 表示随机性最小 0-2
    stream=True
)

for response in responses:
    #print("Raw response:", response)  # 查看完整响应结构
    if response.choices:
        choice = response.choices[0]
        if choice.text:
            print(choice.text, end='', flush=True)

