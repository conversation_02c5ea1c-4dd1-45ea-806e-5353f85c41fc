##思维链
# 加载环境变量
import os
from openai import OpenAI

#加载环境变量
from dotenv import load_dotenv, find_dotenv
_ = load_dotenv(find_dotenv())  # 读取本地 .env 文件，里面定义了 OPENAI_API_KEY

openai = OpenAI(
    api_key=os.getenv("OPENAI_API_KEY"),
    base_url=os.getenv("OPENAI_API_BASE"),
)
#model = 'gpt-3.5-turbo'
# 基于 prompt 生成文本
def get_completion(prompt, model="gpt-3.5-turbo"):
    messages = [{"role": "user", "content": prompt}]
    response = openai.chat.completions.create(
        model=model,
        messages=messages,
        temperature=0,  # 模型输出的随机性，0 表示随机性最小
    )
    return response.choices[0].message.content

# prompt = """这组数中的奇数加起来是偶数：4、8、9、15、12、2、1。\
#             A：将所有奇数相加（9、15、1）得到25。答案为False。\
#             这组数中的奇数加起来是偶数：15、32、5、13、82、7、1。\
#             A："""
def create_cot_prompt(question):
    prompt = f"""
                让我们一步一步地思考解决这个问题。
            
                Question: {question}
            
                Step 1: 理解问题
                这个问题的主要目标是什么？
            
                Step 2: 分解问题
                需要解决的子问题是什么？
            
                Step 3: 解决子问题
                让我们逐一解决每个子问题。
            
                Step 4: 合并解决方案
                让我们结合子问题的解决方案来得到最终答案。
            
                请提供一个详细的、循序渐进的问题解决方案。
            """
    return prompt

question = "两辆汽车从相距500千米的两城同时出发，相向而行。\
            一辆摩托车以每小时80千米的速度在两辆汽车之间不断往返联络。\
            已知这两辆汽车的速度分别是每小时40千米和60千米，\
            求两汽车相遇时，摩托车共行驶了多少千米？"

prompt = create_cot_prompt(question)
response = get_completion(prompt)
print(response)

