{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# 第三章 迭代优化\n", "\n", "当使用 LLM 构建应用程序时，实践层面上很难*第一次尝试*就成功获得适合最终应用的 Prompt。但这并不重要，只要您有一个好的迭代过程来不断改进您的 Prompt，那么您就能够得到一个适合任务的 Prompt。虽然相比训练机器学习模型，在  Prompt 方面一次成功的几率可能会高一些，但正如上所说， Prompt 是否一次完善并不重要。最重要的是**层层迭代**为您的应用程序找到有效  Prompt 的过程。\n", "\n", "因此在本章中，我们将以产品说明书中生成营销文案为例，来展示一些流程框架，并提示您思考如何层层迭代地分析和完善您的 Prompt。\n", "\n", "在吴恩达（Andrew Ng，原教程作者）的机器学习课程中展示过一张图表，说明了机器学习开发的流程。通常是先有一个想法，然后再用以下流程实现：编写代码，获取数据，训练模型，获得实验结果。然后您可以查看结果，分析误差与错误，找出适用领域，甚至可以更改您对具体问题的具体思路或解决方法。此后再次更改实现，并运行另一个实验等，反复迭代，最终获得有效的机器学习模型。在编写基于 LLM 的应用程序的 Prompt 时，流程可能非常相似。您产生了关于要完成的任务的想法后，可以尝试编写第一个 Prompt ，注意要满足上一章说过的两个原则：**清晰明确，并且给系统足够的时间思考**。然后您可以运行并查看结果。如果第一次效果不好，那么迭代的过程就是找出为什么指令不够清晰或为什么没有给算法足够的时间思考，以便改进想法、改进  Prompt 等等，循环多次，直到找到适合您的应用程序的 Prompt。\n", "\n", "很难有适用于世间万物的所谓“最佳  Prompt ”，更好的方法是找到有效的迭代过程，以便您可以快速地找到一个适合您的应用程序的  Prompt 。\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<div class=\"toc\">\n", "    <ul class=\"toc-item\">\n", "        <li><span><a href=\"#一环境配置\" data-toc-modified-id=\"一、环境配置\">一、环境配置</a></span></li>\n", "        <li>\n", "            <span><a href=\"#二任务从产品说明书生成一份营销产品描述\" data-toc-modified-id=\"二、任务——从产品说明书生成一份营销产品描述\">二、任务——从产品说明书生成一份营销产品描述</a></span>\n", "            <ul class=\"toc-item\">\n", "                <li><span><a href=\"#21-问题一生成文本太长\" data-toc-modified-id=\"2.1 问题一：生成文本太长\">2.1 问题一：生成文本太长</a></span></li>\n", "                <li><span><a href=\"#22-问题二抓错文本细节\" data-toc-modified-id=\"2.2 问题二：抓错文本细节\">2.2 问题二：抓错文本细节</a></span></li>\n", "                <li><span><a href=\"#23-问题三添加表格描述\" data-toc-modified-id=\"2.3 问题三：添加表格描述\">2.3 问题三：添加表格描述</a></span></li>\n", "            </ul>\n", "        </li>\n", "    </ul>\n", "</div>"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## 一、环境配置\n", "\n", "同上一章，我们首先需要配置使用 OpenAI API 的环境"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import openai\n", "# 导入第三方库\n", "\n", "openai.api_key = \"sk-...\"\n", "# 设置 API_KEY, 请替换成您自己的 API_KEY\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# 一个封装 OpenAI 接口的函数，参数为 Prompt，返回对应结果\n", "def get_completion(prompt, model=\"gpt-3.5-turbo\"):\n", "    '''\n", "    prompt: 对应的提示词\n", "    model: 调用的模型，默认为 gpt-3.5-turbo(ChatGPT)，有内测资格的用户可以选择 gpt-4\n", "    '''\n", "    messages = [{\"role\": \"user\", \"content\": prompt}]\n", "    response = openai.ChatCompletion.create(\n", "        model=model,\n", "        messages=messages,\n", "        temperature=0, # 模型输出的温度系数，控制输出的随机程度\n", "    )\n", "    # 调用 OpenAI 的 ChatCompletion 接口\n", "    return response.choices[0].message[\"content\"]\n"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## 二、任务——从产品说明书生成一份营销产品描述"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["给定一份椅子的资料页。描述说它属于*中世纪灵感*系列，产自意大利，并介绍了材料、构造、尺寸、可选配件等参数。假设您想要使用这份说明书帮助营销团队为电商平台撰写营销描述稿："]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# 示例：产品说明书\n", "fact_sheet_chair = \"\"\"\n", "OVERVIEW\n", "- Part of a beautiful family of mid-century inspired office furniture, \n", "including filing cabinets, desks, bookcases, meeting tables, and more.\n", "- Several options of shell color and base finishes.\n", "- Available with plastic back and front upholstery (SWC-100) \n", "or full upholstery (SWC-110) in 10 fabric and 6 leather options.\n", "- Base finish options are: stainless steel, matte black, \n", "gloss white, or chrome.\n", "- Chair is available with or without armrests.\n", "- Suitable for home or business settings.\n", "- Qualified for contract use.\n", "\n", "CONSTRUCTION\n", "- 5-wheel plastic coated aluminum base.\n", "- Pneumatic chair adjust for easy raise/lower action.\n", "\n", "DIMENSIONS\n", "- WIDTH 53 CM | 20.87”\n", "- DEPTH 51 CM | 20.08”\n", "- HEIGHT 80 CM | 31.50”\n", "- SEAT HEIGHT 44 CM | 17.32”\n", "- SEAT DEPTH 41 CM | 16.14”\n", "\n", "OPTIONS\n", "- Soft or hard-floor caster options.\n", "- Two choices of seat foam densities: \n", "medium (1.8 lb/ft3) or high (2.8 lb/ft3)\n", "- Armless or 8 position PU armrests \n", "\n", "MATERIALS\n", "SHELL BASE GLIDER\n", "- Cast Aluminum with modified nylon PA6/PA66 coating.\n", "- Shell thickness: 10 mm.\n", "SEAT\n", "- HD36 foam\n", "\n", "COUNTRY OF ORIGIN\n", "- Italy\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Introducing our stunning mid-century inspired office chair, the perfect addition to any home or business setting. Part of a beautiful family of office furniture, including filing cabinets, desks, bookcases, meeting tables, and more, this chair is available in several options of shell color and base finishes to suit your style. Choose from plastic back and front upholstery (SWC-100) or full upholstery (SWC-110) in 10 fabric and 6 leather options.\n", "\n", "The chair is constructed with a 5-wheel plastic coated aluminum base and features a pneumatic chair adjust for easy raise/lower action. It is available with or without armrests and is qualified for contract use. The base finish options are stainless steel, matte black, gloss white, or chrome.\n", "\n", "Measuring at a width of 53 cm, depth of 51 cm, and height of 80 cm, with a seat height of 44 cm and seat depth of 41 cm, this chair is designed for ultimate comfort. You can also choose between soft or hard-floor caster options and two choices of seat foam densities: medium (1.8 lb/ft3) or high (2.8 lb/ft3). The armrests are available in either an armless or 8 position PU option.\n", "\n", "The materials used in the construction of this chair are of the highest quality. The shell base glider is made of cast aluminum with modified nylon PA6/PA66 coating and has a shell thickness of 10 mm. The seat is made of HD36 foam, ensuring maximum comfort and durability.\n", "\n", "This chair is made in Italy and is the perfect combination of style and functionality. Upgrade your workspace with our mid-century inspired office chair today!\n"]}], "source": ["#   Prompt ：基于说明书生成营销描述\n", "prompt = f\"\"\"\n", "Your task is to help a marketing team create a \n", "description for a retail website of a product based \n", "on a technical fact sheet.\n", "\n", "Write a product description based on the information \n", "provided in the technical specifications delimited by \n", "triple backticks.\n", "\n", "Technical specifications: ```{fact_sheet_chair}```\n", "\"\"\"\n", "response = get_completion(prompt)\n", "print(response)\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["# 示例：产品说明书\n", "fact_sheet_chair = \"\"\"\n", "概述\n", "\n", "    美丽的中世纪风格办公家具系列的一部分，包括文件柜、办公桌、书柜、会议桌等。\n", "    多种外壳颜色和底座涂层可选。\n", "    可选塑料前后靠背装饰（SWC-100）或10种面料和6种皮革的全面装饰（SWC-110）。\n", "    底座涂层选项为：不锈钢、哑光黑色、光泽白色或铬。\n", "    椅子可带或不带扶手。\n", "    适用于家庭或商业场所。\n", "    符合合同使用资格。\n", "\n", "结构\n", "\n", "    五个轮子的塑料涂层铝底座。\n", "    气动椅子调节，方便升降。\n", "\n", "尺寸\n", "\n", "    宽度53厘米|20.87英寸\n", "    深度51厘米|20.08英寸\n", "    高度80厘米|31.50英寸\n", "    座椅高度44厘米|17.32英寸\n", "    座椅深度41厘米|16.14英寸\n", "\n", "选项\n", "\n", "    软地板或硬地板滚轮选项。\n", "    两种座椅泡沫密度可选：中等（1.8磅/立方英尺）或高（2.8磅/立方英尺）。\n", "    无扶手或8个位置PU扶手。\n", "\n", "材料\n", "外壳底座滑动件\n", "\n", "    改性尼龙PA6/PA66涂层的铸铝。\n", "    外壳厚度：10毫米。\n", "    座椅\n", "    HD36泡沫\n", "\n", "原产国\n", "\n", "    意大利\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["产品描述：\n", "\n", "我们自豪地推出美丽的中世纪风格办公家具系列，其中包括文件柜、办公桌、书柜、会议桌等。我们的产品采用多种外壳颜色和底座涂层，以满足您的个性化需求。您可以选择塑料前后靠背装饰（SWC-100）或10种面料和6种皮革的全面装饰（SWC-110），以使您的办公室更加舒适和时尚。\n", "\n", "我们的底座涂层选项包括不锈钢、哑光黑色、光泽白色或铬，以满足您的不同需求。椅子可带或不带扶手，适用于家庭或商业场所。我们的产品符合合同使用资格，为您提供更加可靠的保障。\n", "\n", "我们的产品采用五个轮子的塑料涂层铝底座，气动椅子调节，方便升降。尺寸为宽度53厘米|20.87英寸，深度51厘米|20.08英寸，高度80厘米|31.50英寸，座椅高度44厘米|17.32英寸，座椅深度41厘米|16.14英寸，为您提供舒适的使用体验。\n", "\n", "我们的产品还提供软地板或硬地板滚轮选项，两种座椅泡沫密度可选：中等（1.8磅/立方英尺）或高（2.8磅/立方英尺），以及无扶手或8个位置PU扶手，以满足您的不同需求。\n", "\n", "我们的产品采用改性尼龙PA6/PA66涂层的铸铝外壳底座滑动件，外壳厚度为10毫米，座椅采用HD36泡沫，为您提供更加舒适的使用体验。我们的产品原产国为意大利，为您提供更加优质的品质保证。\n"]}], "source": ["#   Prompt ：基于说明书创建营销描述\n", "prompt = f\"\"\"\n", "您的任务是帮助营销团队基于技术说明书创建一个产品的营销描述。\n", "\n", "根据```标记的技术说明书中提供的信息，编写一个产品描述。\n", "\n", "技术说明: ```{fact_sheet_chair}```\n", "\"\"\"\n", "response = get_completion(prompt)\n", "print(response)\n"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## 2.1 问题一：生成文本太长\n", "\n", "它似乎很好地完成了要求，即从技术说明书开始编写产品描述，介绍了一个精致的中世纪风格办公椅。但是当我看到这个时，我会觉得这个太长了。\n", "\n", "所以在上述过程中，我产生想法后写了一个  Prompt ，并得到了结果，但是我对它不是很满意，因为它太长了。所以我澄清我的  Prompt ，要求它限制生成文本长度，要求最多使用50个字。\n"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Introducing our beautiful medieval-style office furniture collection, including filing cabinets, desks, bookcases, and conference tables. Choose from a variety of shell colors and base coatings, with optional plastic or fabric/leather decoration. The chair features a plastic-coated aluminum base with five wheels and pneumatic height adjustment. Perfect for home or commercial use. Made in Italy.\n"]}], "source": ["# 优化后的 Prompt，要求生成描述不多于 50 词\n", "prompt = f\"\"\"\n", "Your task is to help a marketing team create a \n", "description for a retail website of a product based \n", "on a technical fact sheet.\n", "\n", "Write a product description based on the information \n", "provided in the technical specifications delimited by \n", "triple backticks.\n", "\n", "Use at most 50 words.\n", "\n", "Technical specifications: ```{fact_sheet_chair}```\n", "\"\"\"\n", "response = get_completion(prompt)\n", "print(response)\n"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["提取回答并根据空格拆分，答案为54个字，较好地完成了设计要求。"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["54\n"]}], "source": ["lst = response.split()\n", "print(len(lst))"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["中世纪风格办公家具系列，包括文件柜、办公桌、书柜、会议桌等。多种颜色和涂层可选，可带或不带扶手。底座涂层选项为不锈钢、哑光黑色、光泽白色或铬。适用于家庭或商业场所，符合合同使用资格。意大利制造。\n"]}], "source": ["# 优化后的 Prompt，要求生成描述不多于 50 词\n", "prompt = f\"\"\"\n", "您的任务是帮助营销团队基于技术说明书创建一个产品的零售网站描述。\n", "\n", "根据```标记的技术说明书中提供的信息，编写一个产品描述。\n", "\n", "使用最多50个词。\n", "\n", "技术规格：```{fact_sheet_chair}```\n", "\"\"\"\n", "response = get_completion(prompt)\n", "print(response)\n"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/plain": ["97"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["# 由于中文需要分词，此处直接计算整体长度\n", "len(response)"]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["LLM在能堪堪胜任严格的字数限制，但实现得并不精确。此例中，英文输出要求控制在50个词，但有时会输出60或65个单词的内容，但这也还算合理。原因是 LLM 使用分词器（tokenizer）解释文本，但它们往往在计算字符方面表现一般般。有很多不同的方法来尝试控制您得到的输出的长度（如若干句话/词/个汉字/个字母 (characters) 等）。"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## 2.2 问题二：抓错文本细节\n", "\n", "我们继续完善这段推广词，会发现的第二个问题是，这个网站并不是直接向消费者销售，它实际上面向的是家具零售商，他们会更关心椅子的技术细节和材料。在这种情况下，您可以继续修改这个  Prompt ，让它更精确地描述椅子的技术细节。\n", "\n", "解决方法：要求它专注于与目标受众相关的方面。"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Introducing our beautiful medieval-style office furniture collection, including file cabinets, desks, bookcases, and conference tables. Available in multiple shell colors and base coatings, with optional plastic or fabric/leather upholstery. Features a plastic-coated aluminum base with five wheels and pneumatic chair adjustment. Suitable for home or commercial use and made with high-quality materials, including cast aluminum with a modified nylon coating and HD36 foam. Made in Italy.\n"]}], "source": ["# 优化后的 Prompt，说明面向对象，应具有什么性质且侧重于什么方面\n", "prompt = f\"\"\"\n", "Your task is to help a marketing team create a \n", "description for a retail website of a product based \n", "on a technical fact sheet.\n", "\n", "Write a product description based on the information \n", "provided in the technical specifications delimited by \n", "triple backticks.\n", "\n", "The description is intended for furniture retailers, \n", "so should be technical in nature and focus on the \n", "materials the product is constructed from.\n", "\n", "Use at most 50 words.\n", "\n", "Technical specifications: ```{fact_sheet_chair}```\n", "\"\"\"\n", "response = get_completion(prompt)\n", "print(response)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["这款中世纪风格办公家具系列包括文件柜、办公桌、书柜和会议桌等，适用于家庭或商业场所。可选多种外壳颜色和底座涂层，底座涂层选项为不锈钢、哑光黑色、光泽白色或铬。椅子可带或不带扶手，可选软地板或硬地板滚轮，两种座椅泡沫密度可选。外壳底座滑动件采用改性尼龙PA6/PA66涂层的铸铝，座椅采用HD36泡沫。原产国为意大利。\n"]}], "source": ["# 优化后的 Prompt，说明面向对象，应具有什么性质且侧重于什么方面\n", "prompt = f\"\"\"\n", "您的任务是帮助营销团队基于技术说明书创建一个产品的零售网站描述。\n", "\n", "根据```标记的技术说明书中提供的信息，编写一个产品描述。\n", "\n", "该描述面向家具零售商，因此应具有技术性质，并侧重于产品的材料构造。\n", "\n", "使用最多50个单词。\n", "\n", "技术规格： ```{fact_sheet_chair}```\n", "\"\"\"\n", "response = get_completion(prompt)\n", "print(response)"]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["可见，通过修改  Prompt ，模型的关注点倾向了具体特征与技术细节。\n", "\n", "我可能进一步想要在描述的结尾展示出产品ID。因此，我可以进一步改进这个  Prompt ，要求在描述的结尾，展示出说明书中的7位产品ID。"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Introducing our beautiful medieval-style office furniture collection, featuring file cabinets, desks, bookshelves, and conference tables. Available in multiple shell colors and base coatings, with optional plastic or fabric/leather decorations. The chair comes with or without armrests and has a plastic-coated aluminum base with five wheels and pneumatic height adjustment. Suitable for home or commercial use. Made in Italy.\n", "\n", "Product IDs: SWC-100, SWC-110\n"]}], "source": ["# 更进一步，要求在描述末尾包含 7个字符的产品ID\n", "prompt = f\"\"\"\n", "Your task is to help a marketing team create a \n", "description for a retail website of a product based \n", "on a technical fact sheet.\n", "\n", "Write a product description based on the information \n", "provided in the technical specifications delimited by \n", "triple backticks.\n", "\n", "The description is intended for furniture retailers, \n", "so should be technical in nature and focus on the \n", "materials the product is constructed from.\n", "\n", "At the end of the description, include every 7-character \n", "Product ID in the technical specification.\n", "\n", "Use at most 50 words.\n", "\n", "Technical specifications: ```{fact_sheet_chair}```\n", "\"\"\"\n", "response = get_completion(prompt)\n", "print(response)"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["这款中世纪风格的办公家具系列包括文件柜、办公桌、书柜和会议桌等，适用于家庭或商业场所。可选多种外壳颜色和底座涂层，底座涂层选项为不锈钢、哑光黑色、光泽白色或铬。椅子可带或不带扶手，可选塑料前后靠背装饰或10种面料和6种皮革的全面装饰。座椅采用HD36泡沫，可选中等或高密度，座椅高度44厘米，深度41厘米。外壳底座滑动件采用改性尼龙PA6/PA66涂层的铸铝，外壳厚度为10毫米。原产国为意大利。产品ID：SWC-100/SWC-110。\n"]}], "source": ["# 更进一步\n", "prompt = f\"\"\"\n", "您的任务是帮助营销团队基于技术说明书创建一个产品的零售网站描述。\n", "\n", "根据```标记的技术说明书中提供的信息，编写一个产品描述。\n", "\n", "该描述面向家具零售商，因此应具有技术性质，并侧重于产品的材料构造。\n", "\n", "在描述末尾，包括技术规格中每个7个字符的产品ID。\n", "\n", "使用最多50个单词。\n", "\n", "技术规格： ```{fact_sheet_chair}```\n", "\"\"\"\n", "response = get_completion(prompt)\n", "print(response)"]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["以上是许多开发人员通常会经历的  Prompt 开发的迭代过程简短示例。我的建议是，像上一章中所演示的那样，Prompt 应该保持清晰和明确，并在必要时给模型一些思考时间。在这些要求的基础上，常见流程是首先尝试编写一版 Prompt ，看看会发生什么，然后继续迭代完善 Prompt，以逐渐接近所需的结果。许多成功的 Prompt 都是通过这种迭代过程得出的。我将向您展示一个更复杂的 Prompt 示例，可能会让您对 ChatGPT 的能力有更深入的了解。"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## 2.3 问题三：添加表格描述\n", "继续添加指引，要求提取产品尺寸信息并组织成表格，并指定表格的列、表名和格式；再将所有内容格式化为可以在网页使用的 HTML。"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<div>\n", "  <p>Introducing our beautiful collection of medieval-style office furniture, including file cabinets, desks, bookcases, and conference tables. Choose from a variety of shell colors and base coatings. You can opt for plastic front and backrest decoration (SWC-100) or full decoration with 10 fabrics and 6 leathers (SWC-110). Base coating options include stainless steel, matte black, glossy white, or chrome. The chair is available with or without armrests and is suitable for both home and commercial settings. It is contract eligible.</p>\n", "  <p>The structure features a plastic-coated aluminum base with five wheels. The chair is pneumatically adjustable for easy height adjustment.</p>\n", "  <p>Product IDs: SWC-100, SWC-110</p>\n", "  <table>\n", "    <caption>Product Dimensions</caption>\n", "    <tr>\n", "      <td>Width</td>\n", "      <td>20.87 inches</td>\n", "    </tr>\n", "    <tr>\n", "      <td>Depth</td>\n", "      <td>20.08 inches</td>\n", "    </tr>\n", "    <tr>\n", "      <td>Height</td>\n", "      <td>31.50 inches</td>\n", "    </tr>\n", "    <tr>\n", "      <td>Seat Height</td>\n", "      <td>17.32 inches</td>\n", "    </tr>\n", "    <tr>\n", "      <td><PERSON><PERSON></td>\n", "      <td>16.14 inches</td>\n", "    </tr>\n", "  </table>\n", "  <p>Options include soft or hard floor casters. You can choose from two seat foam densities: medium (1.8 pounds/cubic foot) or high (2.8 pounds/cubic foot). The chair is available with or without 8-position PU armrests.</p>\n", "  <p>Materials:</p>\n", "  <ul>\n", "    <li>Shell, base, and sliding parts: cast aluminum coated with modified nylon PA6/PA66. Shell thickness: 10mm.</li>\n", "    <li>Seat: HD36 foam</li>\n", "  </ul>\n", "  <p>Made in Italy.</p>\n", "</div>\n"]}], "source": ["# 要求它抽取信息并组织成表格，并指定表格的列、表名和格式\n", "prompt = f\"\"\"\n", "Your task is to help a marketing team create a \n", "description for a retail website of a product based \n", "on a technical fact sheet.\n", "\n", "Write a product description based on the information \n", "provided in the technical specifications delimited by \n", "triple backticks.\n", "\n", "The description is intended for furniture retailers, \n", "so should be technical in nature and focus on the \n", "materials the product is constructed from.\n", "\n", "At the end of the description, include every 7-character \n", "Product ID in the technical specification.\n", "\n", "After the description, include a table that gives the \n", "product's dimensions. The table should have two columns.\n", "In the first column include the name of the dimension. \n", "In the second column include the measurements in inches only.\n", "\n", "Give the table the title 'Product Dimensions'.\n", "\n", "Format everything as HTML that can be used in a website. \n", "Place the description in a <div> element.\n", "\n", "Technical specifications: ```{fact_sheet_chair}```\n", "\"\"\"\n", "\n", "response = get_completion(prompt)\n", "print(response)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "  <p>Introducing our beautiful collection of medieval-style office furniture, including file cabinets, desks, bookcases, and conference tables. Choose from a variety of shell colors and base coatings. You can opt for plastic front and backrest decoration (SWC-100) or full decoration with 10 fabrics and 6 leathers (SWC-110). Base coating options include stainless steel, matte black, glossy white, or chrome. The chair is available with or without armrests and is suitable for both home and commercial settings. It is contract eligible.</p>\n", "  <p>The structure features a plastic-coated aluminum base with five wheels. The chair is pneumatically adjustable for easy height adjustment.</p>\n", "  <p>Product IDs: SWC-100, SWC-110</p>\n", "  <table>\n", "    <caption>Product Dimensions</caption>\n", "    <tr>\n", "      <td>Width</td>\n", "      <td>20.87 inches</td>\n", "    </tr>\n", "    <tr>\n", "      <td>Depth</td>\n", "      <td>20.08 inches</td>\n", "    </tr>\n", "    <tr>\n", "      <td>Height</td>\n", "      <td>31.50 inches</td>\n", "    </tr>\n", "    <tr>\n", "      <td>Seat Height</td>\n", "      <td>17.32 inches</td>\n", "    </tr>\n", "    <tr>\n", "      <td><PERSON><PERSON></td>\n", "      <td>16.14 inches</td>\n", "    </tr>\n", "  </table>\n", "  <p>Options include soft or hard floor casters. You can choose from two seat foam densities: medium (1.8 pounds/cubic foot) or high (2.8 pounds/cubic foot). The chair is available with or without 8-position PU armrests.</p>\n", "  <p>Materials:</p>\n", "  <ul>\n", "    <li>Shell, base, and sliding parts: cast aluminum coated with modified nylon PA6/PA66. Shell thickness: 10mm.</li>\n", "    <li>Seat: HD36 foam</li>\n", "  </ul>\n", "  <p>Made in Italy.</p>\n", "</div>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 表格是以 HTML 格式呈现的，加载出来\n", "from IPython.display import display, HTML\n", "\n", "display(HTML(response))"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<div>\n", "<h2>中世纪风格办公家具系列椅子</h2>\n", "<p>这款椅子是中世纪风格办公家具系列的一部分，适用于家庭或商业场所。它有多种外壳颜色和底座涂层可选，包括不锈钢、哑光黑色、光泽白色或铬。您可以选择带或不带扶手的椅子，以及软地板或硬地板滚轮选项。此外，您可以选择两种座椅泡沫密度：中等（1.8磅/立方英尺）或高（2.8磅/立方英尺）。</p>\n", "<p>椅子的外壳底座滑动件是改性尼龙PA6/PA66涂层的铸铝，外壳厚度为10毫米。座椅采用HD36泡沫，底座是五个轮子的塑料涂层铝底座，可以进行气动椅子调节，方便升降。此外，椅子符合合同使用资格，是您理想的选择。</p>\n", "<p>产品ID：SWC-100</p>\n", "</div>\n", "\n", "<table>\n", "  <caption>产品尺寸</caption>\n", "  <tr>\n", "    <th>宽度</th>\n", "    <td>20.87英寸</td>\n", "  </tr>\n", "  <tr>\n", "    <th>深度</th>\n", "    <td>20.08英寸</td>\n", "  </tr>\n", "  <tr>\n", "    <th>高度</th>\n", "    <td>31.50英寸</td>\n", "  </tr>\n", "  <tr>\n", "    <th>座椅高度</th>\n", "    <td>17.32英寸</td>\n", "  </tr>\n", "  <tr>\n", "    <th>座椅深度</th>\n", "    <td>16.14英寸</td>\n", "  </tr>\n", "</table>\n"]}], "source": ["# 要求它抽取信息并组织成表格，并指定表格的列、表名和格式\n", "prompt = f\"\"\"\n", "您的任务是帮助营销团队基于技术说明书创建一个产品的零售网站描述。\n", "\n", "根据```标记的技术说明书中提供的信息，编写一个产品描述。\n", "\n", "该描述面向家具零售商，因此应具有技术性质，并侧重于产品的材料构造。\n", "\n", "在描述末尾，包括技术规格中每个7个字符的产品ID。\n", "\n", "在描述之后，包括一个表格，提供产品的尺寸。表格应该有两列。第一列包括尺寸的名称。第二列只包括英寸的测量值。\n", "\n", "给表格命名为“产品尺寸”。\n", "\n", "将所有内容格式化为可用于网站的HTML格式。将描述放在<div>元素中。\n", "\n", "技术规格：```{fact_sheet_chair}```\n", "\"\"\"\n", "\n", "response = get_completion(prompt)\n", "print(response)"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<h2>中世纪风格办公家具系列椅子</h2>\n", "<p>这款椅子是中世纪风格办公家具系列的一部分，适用于家庭或商业场所。它有多种外壳颜色和底座涂层可选，包括不锈钢、哑光黑色、光泽白色或铬。您可以选择带或不带扶手的椅子，以及软地板或硬地板滚轮选项。此外，您可以选择两种座椅泡沫密度：中等（1.8磅/立方英尺）或高（2.8磅/立方英尺）。</p>\n", "<p>椅子的外壳底座滑动件是改性尼龙PA6/PA66涂层的铸铝，外壳厚度为10毫米。座椅采用HD36泡沫，底座是五个轮子的塑料涂层铝底座，可以进行气动椅子调节，方便升降。此外，椅子符合合同使用资格，是您理想的选择。</p>\n", "<p>产品ID：SWC-100</p>\n", "</div>\n", "\n", "<table>\n", "  <caption>产品尺寸</caption>\n", "  <tr>\n", "    <th>宽度</th>\n", "    <td>20.87英寸</td>\n", "  </tr>\n", "  <tr>\n", "    <th>深度</th>\n", "    <td>20.08英寸</td>\n", "  </tr>\n", "  <tr>\n", "    <th>高度</th>\n", "    <td>31.50英寸</td>\n", "  </tr>\n", "  <tr>\n", "    <th>座椅高度</th>\n", "    <td>17.32英寸</td>\n", "  </tr>\n", "  <tr>\n", "    <th>座椅深度</th>\n", "    <td>16.14英寸</td>\n", "  </tr>\n", "</table>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 表格是以 HTML 格式呈现的，加载出来\n", "from IPython.display import display, HTML\n", "\n", "display(HTML(response))"]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["本章的主要内容是 LLM 在开发应用程序中的迭代式  Prompt 开发过程。开发者需要先尝试编写  Prompt ，然后通过迭代逐步完善它，直至得到需要的结果。作为一名高效的提示词工程师（Prompt Engineer），关键在于掌握有效的开发Prompt的过程，而不是去寻求得到“完美的”Prompt。对于一些更复杂的应用程序，可以对多个样本（如数百张说明书）进行  Prompt 的迭代开发，并在样本集上进行评估。\n", "\n", "最后，在更成熟的应用程序中，可以观察多个Prompt在多个样本集上的表现，测试平均或最差性能。但通常，**仅当**应用较成型之后，才推荐您通过这种评估方式，来精益求精。\n", "\n", "请使用 Jupyter Notebook，动手实践本节给出的示例，并尝试不同的变化，查看结果。"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.13"}, "latex_envs": {"LaTeX_envs_menu_present": true, "autoclose": false, "autocomplete": true, "bibliofile": "biblio.bib", "cite_by": "apalike", "current_citInitial": 1, "eqLabelWithNumbers": true, "eqNumInitial": 1, "hotkeys": {"equation": "Ctrl-E", "itemize": "Ctrl-I"}, "labels_anchors": false, "latex_user_defs": false, "report_style_numbering": false, "user_envs_cfg": false}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {}, "toc_section_display": true, "toc_window_display": true}}, "nbformat": 4, "nbformat_minor": 4}